package com.drex.customer.web;

import com.drex.customer.api.RemotePassportService;
import com.drex.customer.api.request.UnbindWalletRequest;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

/**
 * Passport相关的REST接口控制器
 */
@RestController
@RequestMapping("/passport")
public class PassportController {

    private static final Logger log = LoggerFactory.getLogger(PassportController.class);

    @Resource
    private RemotePassportService remotePassportService;

    /**
     * 解绑钱包接口
     * 
     * @param request 解绑钱包请求
     * @return 解绑结果
     */
    @PostMapping("/unbind-wallet")
    public Response<Boolean> unbindWallet(@RequestBody UnbindWalletRequest request) {
        log.info("Received unbind wallet request: passportId={}, walletAddress={}", 
                request.getPassportId(), request.getWalletAddress());
        
        try {
            return remotePassportService.unbindWallet(request);
        } catch (Exception e) {
            log.error("Failed to unbind wallet", e);
            return Response.error("UNKNOWN_ERROR", "解绑钱包失败");
        }
    }

    /**
     * 为历史用户一次性绑定社媒信息同时完成任务
     */
    @PostMapping("/asia-tour/bind-social")
    public Response<Boolean> bindSocial() {
        log.info("Received asia-tour bind social request");
        try {
            //先查询出asia-tour用户

        } catch (Exception e) {
            log.error("Failed to bind social", e);
            return Response.error("UNKNOWN_ERROR", "绑定社媒失败");
        }
        return null;
    }
}
