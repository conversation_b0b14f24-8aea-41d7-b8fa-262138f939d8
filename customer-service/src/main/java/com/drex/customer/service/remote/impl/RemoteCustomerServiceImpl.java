package com.drex.customer.service.remote.impl;

import com.drex.customer.api.ErrorCode;
import com.drex.customer.api.RemoteCustomerService;
import com.drex.customer.api.request.CustomerReferralDTO;
import com.drex.customer.api.request.AddWaitCreatorListRequest;
import com.drex.customer.api.request.AddWaitDeveloperListRequest;
import com.drex.customer.api.request.AddWaitListRequest;
import com.drex.customer.service.business.CustomerService;
import com.drex.customer.service.business.InviteService;
import com.kikitrade.framework.common.model.Response;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

@DubboService
@Slf4j
public class RemoteCustomerServiceImpl implements RemoteCustomerService {

    @Resource
    private CustomerService customerService;
    @Resource
    private InviteService inviteService;
    @Resource
    private CustomerCodeTransactionBuilder customerCodeTransactionBuilder;

    @Override
    public Response bindInviteCode(String customerId, String inviteCode) {
        CustomerReferralDTO customerReferralDTO = new CustomerReferralDTO();
        customerReferralDTO.setInviteCode(inviteCode);
        customerReferralDTO.setCustomerId(customerId);
        return inviteService.bindInviteCode(customerReferralDTO)
                ? Response.success(null)
                : Response.error(ErrorCode.INVITE_CODE_INVALID.getCode(), ErrorCode.INVITE_CODE_INVALID.getMessage());
    }

    @Override
    public Response<Long> countByReferrerId(String customerId) {
        return Response.success(inviteService.countByReferrerId(customerId));
    }

    @Override
    public Response<String> getReferrerByCustomerId(String customerId) {
        return Response.success(inviteService.getReferrerByCustomerId(customerId));
    }

    @Override
    public Response<Boolean> updateLevel(String customerId, String kycLevel) {
        Boolean res = customerService.updateLevel(customerId, kycLevel);
        return Response.success(res);
    }

    @Override
    public Response<Boolean> addWaitList(AddWaitListRequest addWaitListRequest) {
        log.info("addWaitListRequest: {}", addWaitListRequest);
        Boolean res = customerService.addWaitList(addWaitListRequest);
        return Response.success(res);
    }

    @Override
    public Response<Boolean> addWaitCreatorList(AddWaitCreatorListRequest addWaitListRequest) {
        log.info("addWaitCreatorList: {}", addWaitListRequest);
        boolean res = customerService.addWaitList(addWaitListRequest);
        return Response.success(res);
    }

    @Override
    public Response<Boolean> addWaitDeveloperList(AddWaitDeveloperListRequest addWaitListRequest) {
        log.info("addWaitDeveloperList: {}", addWaitListRequest);
        boolean res = customerService.addWaitList(addWaitListRequest);
        return Response.success(res);
    }

    @Override
    public Response<List<String>> getAsiaTourUsers() {
       customerCodeTransactionBuilder.getCodesUser("DEFAULT_INVITE", "1697078400000", "1697164800000");
        return null;
    }
}
