package com.drex.customer.api;

import com.drex.customer.api.request.AddWaitCreatorListRequest;
import com.drex.customer.api.request.AddWaitDeveloperListRequest;
import com.drex.customer.api.request.AddWaitListRequest;
import com.kikitrade.framework.common.model.Response;

import java.util.List;

public interface RemoteCustomerService {

    Response bindInviteCode(String customerId, String inviteCode);

    Response<Long> countByReferrerId(String customerId);

    Response<String> getReferrerByCustomerId(String customerId);

    Response<Boolean> updateLevel(String customerId, String kycLevel);

    /**
     * 添加用户预订阅列表
     * @param addWaitListRequest
     * @return
     */
    Response<Boolean> addWaitList(AddWaitListRequest addWaitListRequest);

    /**
     * 添加创作者预订阅列表
     * @param addWaitListRequest
     * @return
     */
    Response<Boolean> addWaitCreatorList(AddWaitCreatorListRequest addWaitListRequest);

    /**
     * 添加开发者预订阅列表
     * @param addWaitListRequest
     * @return
     */
    Response<Boolean> addWaitDeveloperList(AddWaitDeveloperListRequest addWaitListRequest);

    Response<List<String>> getAsiaTourUsers();
}
